import { download, get, post } from '../request'
import { 
    ITourismProductsFilter, 
    ITourismProducts,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const tourismProductsApi = {
    list: (params: ITourismProductsFilter): Promise<IPageResponse<ITourismProducts>> => {
        return get('/mice-bid/api/tourismProducts/list', params)
    },

    get: (id: number): Promise<ITourismProducts> => {
        return get('/mice-bid/api/tourismProducts/get', {
            id
        })
    },

    save: (params: ITourismProducts): Promise<Result> => {
        return post('/mice-bid/api/tourismProducts/save', params)
    },

    edit: (params: ITourismProducts): Promise<Result> => {
        return post('/mice-bid/api/tourismProducts/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('/mice-bid/api/tourismProducts/delete', { id })
    },
}
