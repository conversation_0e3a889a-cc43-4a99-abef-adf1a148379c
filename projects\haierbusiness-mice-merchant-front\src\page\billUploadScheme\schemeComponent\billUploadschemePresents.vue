<script setup lang="ts">
// 方案互动-礼品方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import GiftSelectionModal from '@haierbusiness-front/components/mice/giftSelectionModal/index.vue';

import dayjs, { Dayjs } from 'dayjs';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['presentPriceEmit', 'schemePresentEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败
const showGiftModal = ref(false);
const currentGiftIndex = ref(0);
const giftModes = ref<Record<number, 'select' | 'manual'>>({});

// 礼品需求表单
const formState = reactive<DemandSubmitObj>({
  // 礼品需求
  presents: [],
});
const currentScheme = reactive({});
// 选择礼品回调
const handleSelectGift = (gift: any, index: number) => {
  console.log(gift);
  console.log(currentScheme);
  newSchemeList.value.forEach((curItem, curIndex) => {
    if (currentScheme.miceDemandPresentId === curItem.miceDemandPresentId) {
      curItem.presentDetails[0].productId = gift.id;
      curItem.presentDetails[0].productMerchantId = gift.merchantId;
      curItem.presentDetails[0].productName = gift.presentName;
      curItem.presentDetails[0].personSpecs = gift.specComb;
      curItem.presentDetails[0].unitPrice = gift.salePrice;
      // 移除自动计算总预算的逻辑，保持用户原有的总预算不变
    }
  });
  changePrice();
};
const changeMode = (val, item) => {
  if (val == 1) {
    Object.assign(currentScheme, item), (showGiftModal.value = true);
  }
};
const getPrice = (item) => {
  // 移除自动计算逻辑，数量和总预算独立
  changePrice();
};
const changePrice = () => {
  // 小计
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    if (e.schemeTotalPrice) {
      subtotal.value += e.schemeTotalPrice;
    }
  });

  emit('presentPriceEmit', subtotal.value);
};

watch(
  () => [props.demandInfo, props.schemeCacheInfo],
  () => {
    // console.log('%c [ 礼品方案 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.demandInfo.presents);

    oldSchemeList.value = JSON.parse(JSON.stringify(props.demandInfo))?.presents || [];

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      const cacheList = props.schemeCacheInfo?.presents || [];
      newSchemeList.value = JSON.parse(
        JSON.stringify(
          cacheList.map((item: any) => {
            // 为账单上传模式添加必需字段
            const baseItem: any = { ...item, optionType: item.optionType || 0 };

            if (props.schemeType === 'billUpload') {
              // 添加账单上传必需的字段
              baseItem.invoiceTempId = item.invoiceTempId || Date.now() + Math.random();
              baseItem.statementTempId = item.statementTempId || Date.now() + Math.random();
              baseItem.billTotalPrice = item.billTotalPrice || item.schemeTotalPrice || 0;
              baseItem.miceBillAttachmentInvoiceId = item.miceBillAttachmentInvoiceId || null;
              baseItem.miceBillAttachmentStatementId = item.miceBillAttachmentStatementId || null;

              // 为presentDetails添加账单字段
              if (baseItem.presentDetails && baseItem.presentDetails.length > 0) {
                baseItem.presentDetails = baseItem.presentDetails.map((detail: any) => ({
                  ...detail,
                  billUnitPrice: detail.billUnitPrice || detail.schemeUnitPrice || detail.unitPrice || 0,
                  billPersonNum: detail.billPersonNum || detail.schemePersonNum || 0,
                }));
              }
            }

            return baseItem;
          }),
        ),
      );

      oldSchemeList.value = JSON.parse(JSON.stringify(cacheList));
      oldSchemeList.value.forEach((e: any) => {
        e.productName = e.presentDetails[0].productName;
        e.personNum = e.presentDetails[0].schemePersonNum;
        e.unit = e.presentDetails[0].unit;
        e.deliveryDate = e.presentDetails[0].deliveryDate;
        e.personSpecs = e.presentDetails[0].personSpecs;
      });
    } else {
      const demandData = JSON.parse(JSON.stringify(props.demandInfo))?.presents || [];
      newSchemeList.value = demandData.map((e: any) => {
        const baseItem: any = {
          miceDemandPresentId: e.id,
          demandTotalPrice: e.demandTotalPrice,
          schemeTotalPrice: e.demandTotalPrice,
          optionType: e.optionType,
          description: e.description,
          presentDetails: [
            {
              miceDemandPresentId: e.id,
              deliveryDate: e.deliveryDate,
              schemePersonNum: e.personNum,
              unitPrice: e.unitPrice,
              productId: e.productId,
              productMerchantId: e.productMerchantId,
              productName: e.productName,
              unit: e.unit,
              personSpecs: e.personSpecs,
            },
          ],
        };

        // 为账单上传模式添加必需字段
        if (props.schemeType === 'billUpload') {
          baseItem.invoiceTempId = Date.now() + Math.random();
          baseItem.statementTempId = Date.now() + Math.random();
          baseItem.billTotalPrice = e.demandTotalPrice || 0;
          baseItem.miceBillAttachmentInvoiceId = null;
          baseItem.miceBillAttachmentStatementId = null;

          // 为presentDetails添加账单字段
          baseItem.presentDetails[0].billUnitPrice = e.unitPrice || 0;
          baseItem.presentDetails[0].billPersonNum = e.personNum || 0;
        }

        return baseItem;
      });
    }

    // 小计
    changePrice();
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['礼品类型', '数量', '单位', '总预算', '送达日期', '礼品描述'];

// 不可选日期
const disabledDate = (current: Dayjs) => {
  // 当前日期之前的日期都禁用
  return current && current < dayjs().endOf('day');
};

// 暂存
const presentTempSave = () => {
  // 为账单上传模式处理数据结构
  const processedData = props.schemeType === 'billUpload'
    ? newSchemeList.value.map((item: any) => ({
        ...item,
        // 确保账单字段存在
        invoiceTempId: item.invoiceTempId || Date.now() + Math.random(),
        statementTempId: item.statementTempId || Date.now() + Math.random(),
        billTotalPrice: item.billTotalPrice || item.schemeTotalPrice || 0,
        presentDetails: item.presentDetails?.map((detail: any) => ({
          ...detail,
          billUnitPrice: detail.billUnitPrice || detail.schemeUnitPrice || detail.unitPrice || 0,
          billPersonNum: detail.billPersonNum || detail.schemePersonNum || 0,
        })) || []
      }))
    : [...newSchemeList.value];

  emit('schemePresentEmit', processedData);
};
// 校验
const presentSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e: any, i: number) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (!e.presentDetails || e.presentDetails.length === 0) {
      isVerPassed = false;
      return;
    }

    const presentDetail = e.presentDetails[0] || {};

    if (!presentDetail.productName) {
      message.error('请输入礼品方案' + (i + 1) + ' 礼品类型');

      isVerPassed = false;
      return;
    }
    if (!presentDetail.schemePersonNum) {
      message.error('请输入礼品方案' + (i + 1) + ' 数量');

      isVerPassed = false;
      return;
    }
    if (!presentDetail.unit) {
      message.error('请输入礼品方案' + (i + 1) + ' 单位');

      isVerPassed = false;
      return;
    }

    if (!e.schemeTotalPrice) {
      message.error('请输入礼品方案' + (i + 1) + ' 总预算');

      isVerPassed = false;
      return;
    }

    if (!presentDetail.deliveryDate) {
      message.error('请输入礼品方案' + (i + 1) + ' 送达日期');

      isVerPassed = false;
      return;
    }

    if (!presentDetail.personSpecs) {
      message.error('请输入礼品方案' + (i + 1) + ' 礼品描述');

      isVerPassed = false;
      return;
    }
  });

  if (isVerPassed) {
    presentTempSave();
  }

  return isVerPassed;
};

defineExpose({ presentSub, presentTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 礼品方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>礼品方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '礼品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.productName || '-' }}
                </template>
                {{ item.productName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personNum || '-' }}
                </template>
                {{ item.personNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
                </template>
                {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.deliveryDate || '-' }}
                </template>
                {{ item.deliveryDate || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personSpecs || '-' }}
                </template>
                {{ item.personSpecs || '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '礼品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div
                style="border: none"
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].productName ? 'error_tip' : '',
                ]"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView' || props.schemeType === 'billUpload'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.productName || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.productName || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.productName">
                    {{ item.presentDetails[0]?.productName || '-' }}
                  </template>
                  <a-row>
                    <a-col :span="14" style="border-right: 1px solid #e5e6eb">
                      <a-input
                        @click="
                          item.optionType == 1 ? (Object.assign(currentScheme, item), (showGiftModal = true)) : ''
                        "
                        v-model:value="item.presentDetails[0].productName"
                        placeholder="礼品类型"
                        :maxlength="500"
                        :bordered="false"
                        allow-clear
                      />
                    </a-col>
                    <a-col :span="10" style="border: 1px solid #1868db; border-radius: 2px">
                      <a-select
                        class="select-mode"
                        @change="
                          (val: any) => {
                            changeMode(val, item);
                          }
                        "
                        v-model:value="item.optionType"
                      >
                        <a-select-option :value="0">手动</a-select-option>
                        <a-select-option :value="1">选择</a-select-option>
                      </a-select>
                    </a-col>
                  </a-row>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].schemePersonNum ? 'error_tip' : '',
                ]"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.schemePersonNum">
                    {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.presentDetails[0].schemePersonNum"
                    placeholder="请输入数量"
                    @blur="getPrice(item)"
                    :bordered="false"
                    :controls="false"
                    :min="1"
                    :max="99999"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.presentDetails[0].unit ? 'error_tip' : '']"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView' || props.schemeType === 'billUpload'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.unit || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.unit || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.unit">
                    {{ item.presentDetails[0]?.unit || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.presentDetails[0].unit"
                    style="width: calc(100% - 30px)"
                    placeholder="请输入单位"
                    :maxlength="50"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.schemeTotalPrice ? 'error_tip' : '']"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                    </template>
                    {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.schemeTotalPrice">
                    {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.schemeTotalPrice"
                    @blur="changePrice()"
                    placeholder="请输入总预算"
                    :bordered="false"
                    :controls="false"
                    :min="0.01"
                    :max="item.demandTotalPrice"
                    :precision="2"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].deliveryDate ? 'error_tip' : '',
                ]"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView' || props.schemeType === 'billUpload'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.deliveryDate || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.deliveryDate || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.deliveryDate">
                    {{ item.presentDetails[0]?.deliveryDate || '-' }}
                  </template>
                  <a-date-picker
                    v-model:value="item.presentDetails[0].deliveryDate"
                    :disabledDate="disabledDate"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    style="width: 100%"
                    :bordered="false"
                    :allow-clear="false"
                  />
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].personSpecs ? 'error_tip' : '',
                ]"
              >
                <div class="pl12" v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView' || props.schemeType === 'billUpload'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.personSpecs || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.personSpecs || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.personSpecs">
                    {{ item.presentDetails[0]?.personSpecs || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.presentDetails[0].personSpecs"
                    style="width: calc(100% - 30px)"
                    placeholder="礼品描述"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeTotalPrice ? '¥' + formatNumberThousands(item.schemeTotalPrice) : '-' }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeTotalPrice">
                {{ item.schemeTotalPrice + '(元/总预算)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
    <GiftSelectionModal
      v-model:visible="showGiftModal"
      :current-gift-index="currentGiftIndex"
      @select-gift="handleSelectGift"
    />
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_value {
    position: relative;
    /* 住宿人数 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      /* padding: 0; */
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;
      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
.modal-click {
  cursor: pointer;
}
:deep(.ant-select, .select-mode) {
  width: 100%;
  .ant-select-selector {
    border: none;
  }
  // position: absolute;
  // top: 8px;
  // left: 5px;
  width: 100%;
}
</style>
